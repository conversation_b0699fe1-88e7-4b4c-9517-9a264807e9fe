option(
  'nolibc',
  type: 'boolean',
  value: false,
  description: 'Do not use any C standard library functions (for exotic build-envs like kernel drivers)',
  yield: true,
)
option(
  'doc',
  type: 'feature',
  description: 'Build doxygen documentation (requires Doxygen)',
)
option(
  'examples',
  type: 'feature',
  description: 'Build examples',
)
option(
  'tests',
  type: 'feature',
  description: 'Build tests',
)
