== [    BASIC ] ============================================================================================
   MNEMONIC: vpaddd [ENC: VEX, MAP: 0F, OPC: 0xFE]
     LENGTH:  5
        SSZ: 64
       EOSZ: 32
       EASZ: 64
   CATEGORY: AVX
    ISA-SET: AVX
    ISA-EXT: AVX
 EXCEPTIONS: AVX4
 ATTRIBUTES: HAS_MODRM HAS_VEX ACCEPTS_SEGMENT 
  OPTIMIZED: C5 09 FE 07 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    128      4      32       INT                         xmm8
 1   REGISTER    EXPLICIT       R        NDSNDD    128      4      32       INT                        xmm14
 2     MEMORY    EXPLICIT       R      MODRM_RM    128      4      32       INT  TYPE  =                 MEM
                                                                                 SEG   =                  ds
                                                                                 BASE  =                 rdi
                                                                                 INDEX =                none
                                                                                 SCALE =                   0
                                                                                 DISP  =  0x0000000000000000
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 128
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vpaddd (%rdi), %xmm14, %xmm8
   RELATIVE: vpaddd (%rdi), %xmm14, %xmm8

== [    INTEL ] ============================================================================================
   ABSOLUTE: vpaddd xmm8, xmm14, xmmword ptr ds:[rdi]
   RELATIVE: vpaddd xmm8, xmm14, xmmword ptr ds:[rdi]

== [ SEGMENTS ] ============================================================================================
C4 21 09 FE 07 
:        :  :..MODRM
:        :..OPCODE
:..VEX
