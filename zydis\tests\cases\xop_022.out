== [    BASIC ] ============================================================================================
   MNEMONIC: vpshaw [ENC: XOP, MAP: XOP9, OPC: 0x99]
     LENGTH:  5
        SSZ: 64
       EOSZ: 64
       EASZ: 64
   CATEGORY: XOP
    ISA-SET: XOP
    ISA-EXT: XOP
 EXCEPTIONS: NONE
 ATTRIBUTES: HAS_MODRM HAS_XOP 
  OPTIMIZED: 8F 49 00 99 E6 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    128      8      16       INT                        xmm12
 1   REGISTER    EXPLICIT       R        NDSNDD    128      8      16       INT                        xmm14
 2   REGISTER    EXPLICIT       R      MODRM_RM    128      8      16       INT                        xmm15
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 128
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vpshaw %xmm15, %xmm14, %xmm12
   RELATIVE: vpshaw %xmm15, %xmm14, %xmm12

== [    INTEL ] ============================================================================================
   ABSOLUTE: vpshaw xmm12, xmm14, xmm15
   RELATIVE: vpshaw xmm12, xmm14, xmm15

== [ SEGMENTS ] ============================================================================================
8F 49 88 99 E7 
:        :  :..MODRM
:        :..OPCODE
:..XOP
