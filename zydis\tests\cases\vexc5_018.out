== [    BASIC ] ============================================================================================
   MNEMONIC: vpmullw [ENC: VEX, MAP: 0F, OPC: 0xD5]
     LENGTH:  4
        SSZ: 64
       EOSZ: 32
       EASZ: 64
   CATEGORY: AVX2
    ISA-SET: AVX2
    ISA-EXT: AVX2
 EXCEPTIONS: AVX4
 ATTRIBUTES: HAS_MODRM HAS_VEX 
  OPTIMIZED: C5 0D D5 E6 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    256     16      16       INT                        ymm12
 1   REGISTER    EXPLICIT       R        NDSNDD    256     16      16       INT                        ymm14
 2   REGISTER    EXPLICIT       R      MODRM_RM    256     16      16       INT                         ymm6
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 256
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vpmullw %ymm6, %ymm14, %ymm12
   RELATIVE: vpmullw %ymm6, %ymm14, %ymm12

== [    INTEL ] ============================================================================================
   ABSOLUTE: vpmullw ymm12, ymm14, ymm6
   RELATIVE: vpmullw ymm12, ymm14, ymm6

== [ SEGMENTS ] ============================================================================================
C5 0D D5 E6 
:     :  :..MODRM
:     :..OPCODE
:..VEX
