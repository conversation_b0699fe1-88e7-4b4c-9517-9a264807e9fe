== [    BASIC ] ============================================================================================
   MNEMONIC: vpcomq [ENC: XOP, MAP: XOP8, OPC: 0xCF]
     LENGTH: 10
        SSZ: 64
       EOSZ: 32
       EASZ: 64
   CATEGORY: XOP
    ISA-SET: XOP
    ISA-EXT: XOP
 EXCEPTIONS: NONE
 ATTRIBUTES: HAS_MODRM HAS_XOP ACCEPTS_SEGMENT 
  OPTIMIZED: 8F 68 50 CF A1 6C B8 7F 15 C1 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    128      2      64       INT                        xmm12
 1   REGISTER    EXPLICIT       R        NDSNDD    128      2      64       INT                         xmm5
 2     MEMORY    EXPLICIT       R      MODRM_RM    128      2      64       INT  TYPE  =                 MEM
                                                                                 SEG   =                  ds
                                                                                 BASE  =                 rcx
                                                                                 INDEX =                none
                                                                                 SCALE =                   0
                                                                                 DISP  =  0x00000000157FB86C
 3  IMMEDIATE    EXPLICIT       R         UIMM8      8      1       8      UINT  [U A  8] 0x00000000000000C1
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 128
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vpcomq $0xC1, 0x157FB86C(%rcx), %xmm5, %xmm12
   RELATIVE: vpcomq $0xC1, 0x157FB86C(%rcx), %xmm5, %xmm12

== [    INTEL ] ============================================================================================
   ABSOLUTE: vpcomq xmm12, xmm5, xmmword ptr ds:[rcx+0x157FB86C], 0xC1
   RELATIVE: vpcomq xmm12, xmm5, xmmword ptr ds:[rcx+0x157FB86C], 0xC1

== [ SEGMENTS ] ============================================================================================
8F 68 50 CF A1 6C B8 7F 15 C1 
:        :  :  :           :..IMM
:        :  :  :..DISP
:        :  :..MODRM
:        :..OPCODE
:..XOP
