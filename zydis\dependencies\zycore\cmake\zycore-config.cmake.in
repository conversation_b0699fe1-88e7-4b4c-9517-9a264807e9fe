set(zycore_VERSION @PROJECT_VERSION@)

@PACKAGE_INIT@

include(CMakeFindDependencyMacro)
if (${CMAKE_SYSTEM_NAME} STREQUAL "Linux" AND NOT @ZYAN_NO_LIBC@)
    find_dependency(Threads)
endif()

include("${CMAKE_CURRENT_LIST_DIR}/zyan-functions.cmake")

include("${CMAKE_CURRENT_LIST_DIR}/zycore-targets.cmake")

set_and_check(zycore_INCLUDE_DIR "${PACKAGE_PREFIX_DIR}/@CMAKE_INSTALL_INCLUDEDIR@")
set_and_check(zycore_LIB_DIR "${PACKAGE_PREFIX_DIR}/@CMAKE_INSTALL_LIBDIR@")

check_required_components(zycore)
