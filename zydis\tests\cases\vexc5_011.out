== [    BASIC ] ============================================================================================
   MNEMONIC: jkzd [ENC: VEX, MAP: 0F, OPC: 0x84]
     LENGTH:  8
        SSZ: 64
       EOSZ: 64
       EASZ: 64
   CATEGORY: COND_BR
    ISA-SET: KNCJKBR
    ISA-EXT: KNCV
 EXCEPTIONS: NONE
 ATTRIBUTES: HAS_VEX IS_RELATIVE 
  OPTIMIZED: C5 E8 84 E3 7C FE 42 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W        NDSNDD     16     16       1       INT                           k2
 1  IMMEDIATE    EXPLICIT       R        JIMM32     32      1      32       INT  [S R 32] 0x0000000042FE7CE3
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 128
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: jkzd 0x0000000042FE7CEB, %k2
   RELATIVE: jkzd +0x42FE7CEB, %k2

== [    INTEL ] ============================================================================================
   ABSOLUTE: jkzd k2, 0x0000000042FE7CEB
   RELATIVE: jkzd k2, +0x42FE7CEB

== [ SEGMENTS ] ============================================================================================
65 C5 E8 84 E3 7C FE 42 
:  :     :  :..IMM
:  :     :..OPCODE
:  :..VEX
:..PREFIXES
