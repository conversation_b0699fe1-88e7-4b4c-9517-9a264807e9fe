// MultipleFiles/instruction_decoder.cpp
#include <algorithm>
#include <array>
#include <cstring>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <unordered_set>

#include "cpu/decoded_instruction.h"
#include "cpu/instruction_decoder.h"
#include "register.h"
#include <spdlog/spdlog.h>

// Include Zydis headers
#include <Zydis/Zydis.h>

namespace x86_64 {
struct DecodeException : std::runtime_error {
  explicit DecodeException(const std::string &msg) : std::runtime_error(msg) {}
};

InstructionDecoder::InstructionDecoder() {
  SPDLOG_INFO("InstructionDecoder constructed with <PERSON><PERSON><PERSON> integration");

  // Initialize Zydis decoder
  ZydisDecoderInit(&decoder, ZYDIS_MACHINE_MODE_LONG_64, ZYDIS_STACK_WIDTH_64);
  ZydisFormatterInit(&formatter, ZYDIS_FORMATTER_STYLE_INTEL);

  // Initialize statistics
  stats.clear();

  SPDLOG_INFO("InstructionDecoder ready with <PERSON><PERSON><PERSON> backend");
}

DecoderErrorInfo InstructionDecoder::Decode(uint64_t addr,
                                            const uint8_t *buffer,
                                            size_t bufferSize,
                                            DecodedInstruction &instr) {
  if (!buffer || bufferSize == 0) {
    spdlog::error("Decode failed at 0x{:x}: null or empty buffer", addr);
    return {DecoderError::BufferOverflow,
            "Null or empty buffer at 0x" + std::to_string(addr)};
  }

  instr.reset();

  // Use Zydis to decode the instruction
  ZydisDecodedInstruction zydisInstr;
  ZydisDecodedOperand zydisOperands[ZYDIS_MAX_OPERAND_COUNT];

  ZydisDecoderContext context;
  ZyanStatus status = ZydisDecoderDecodeInstruction(&decoder, &context, buffer,
                                                    bufferSize, &zydisInstr);

  // Decode operands separately
  if (ZYAN_SUCCESS(status)) {
    ZyanStatus operandStatus =
        ZydisDecoderDecodeOperands(&decoder, &context, &zydisInstr,
                                   zydisOperands, zydisInstr.operand_count);
    if (!ZYAN_SUCCESS(operandStatus)) {
      spdlog::warn("Failed to decode operands at 0x{:x}: status=0x{:x}", addr,
                   operandStatus);
    }
  }

  if (!ZYAN_SUCCESS(status)) {
    spdlog::error("Zydis decode failed at 0x{:x}: status=0x{:x}", addr, status);

    if (status == ZYDIS_STATUS_NO_MORE_DATA) {
      return {DecoderError::IncompleteInstruction,
              "Incomplete instruction at 0x" + std::to_string(addr)};
    } else if (status == ZYDIS_STATUS_DECODING_ERROR) {
      return {DecoderError::InvalidInstruction,
              "Invalid instruction at 0x" + std::to_string(addr)};
    } else {
      return {DecoderError::UnknownOpcode,
              "Unknown opcode at 0x" + std::to_string(addr)};
    }
  }

  // Convert Zydis instruction to our format
  instr.length = zydisInstr.length;
  instr.opcode = zydisInstr.opcode;
  instr.instType = MapZydisToInstructionType(zydisInstr.mnemonic);
  instr.operandCount = std::min(static_cast<uint8_t>(zydisInstr.operand_count),
                                static_cast<uint8_t>(4));

  // Extract condition code for conditional branches
  if (zydisInstr.meta.category == ZYDIS_CATEGORY_COND_BR) {
    instr.conditionCode = MapZydisToConditionCode(zydisInstr.meta.branch_type);
  }

  // Convert operands
  for (uint8_t i = 0; i < instr.operandCount; ++i) {
    ConvertZydisOperand(zydisOperands[i], instr.operands[i]);
  }

  // Extract prefixes and other information
  if (zydisInstr.attributes & ZYDIS_ATTRIB_HAS_REP) {
    instr.repPrefix = true;
  }
  if (zydisInstr.attributes & ZYDIS_ATTRIB_HAS_REPE) {
    instr.repePrefix = true;
  }
  if (zydisInstr.attributes & ZYDIS_ATTRIB_HAS_REPNE) {
    instr.repnePrefix = true;
  }
  if (zydisInstr.attributes & ZYDIS_ATTRIB_HAS_LOCK) {
    instr.lockPrefix = true;
  }
  if (zydisInstr.attributes & ZYDIS_ATTRIB_HAS_OPERANDSIZE) {
    instr.operandSizeOverride = true;
  }
  if (zydisInstr.attributes & ZYDIS_ATTRIB_HAS_ADDRESSSIZE) {
    instr.addressSizeOverride = true;
  }

  // Handle REX prefix
  if (zydisInstr.attributes & ZYDIS_ATTRIB_HAS_REX) {
    // For now, just set a flag that REX is present
    instr.rex = 0x40; // Basic REX prefix indicator
  }

  // Handle VEX/EVEX
  if (zydisInstr.attributes & ZYDIS_ATTRIB_HAS_VEX) {
    instr.isVex = true;
    // Extract VEX information if needed
  }
  if (zydisInstr.attributes & ZYDIS_ATTRIB_HAS_EVEX) {
    instr.isEvex = true;
    // Extract EVEX information if needed
  }

  // Update statistics
  stats[instr.instType]++;

  spdlog::trace("Decoded instruction at 0x{:x}: type={}, length={}", addr,
                static_cast<int>(instr.instType), instr.length);

  return {DecoderError::Success, ""};
}

// Helper method implementations for Zydis integration
InstructionType
InstructionDecoder::MapZydisToInstructionType(ZydisMnemonic mnemonic) const {
  switch (mnemonic) {
  case ZYDIS_MNEMONIC_NOP:
    return InstructionType::Nop;
  case ZYDIS_MNEMONIC_MOV:
    return InstructionType::Mov;
  case ZYDIS_MNEMONIC_ADD:
    return InstructionType::Add;
  case ZYDIS_MNEMONIC_SUB:
    return InstructionType::Sub;
  case ZYDIS_MNEMONIC_MUL:
    return InstructionType::Mul;
  case ZYDIS_MNEMONIC_IMUL:
    return InstructionType::Imul;
  case ZYDIS_MNEMONIC_DIV:
    return InstructionType::Div;
  case ZYDIS_MNEMONIC_IDIV:
    return InstructionType::Idiv;
  case ZYDIS_MNEMONIC_AND:
    return InstructionType::And;
  case ZYDIS_MNEMONIC_OR:
    return InstructionType::Or;
  case ZYDIS_MNEMONIC_XOR:
    return InstructionType::Xor;
  case ZYDIS_MNEMONIC_NOT:
    return InstructionType::Not;
  case ZYDIS_MNEMONIC_NEG:
    return InstructionType::Neg;
  case ZYDIS_MNEMONIC_CMP:
    return InstructionType::Cmp;
  case ZYDIS_MNEMONIC_TEST:
    return InstructionType::Test;
  case ZYDIS_MNEMONIC_JMP:
    return InstructionType::Jmp;
  case ZYDIS_MNEMONIC_JZ:
  case ZYDIS_MNEMONIC_JNZ:
  case ZYDIS_MNEMONIC_JL:
  case ZYDIS_MNEMONIC_JLE:
  case ZYDIS_MNEMONIC_JB:
  case ZYDIS_MNEMONIC_JBE:
  case ZYDIS_MNEMONIC_JO:
  case ZYDIS_MNEMONIC_JNO:
  case ZYDIS_MNEMONIC_JS:
  case ZYDIS_MNEMONIC_JNS:
  case ZYDIS_MNEMONIC_JP:
  case ZYDIS_MNEMONIC_JNP:
  case ZYDIS_MNEMONIC_JCXZ:
  case ZYDIS_MNEMONIC_JECXZ:
  case ZYDIS_MNEMONIC_JRCXZ:
    return InstructionType::Jcc;
  case ZYDIS_MNEMONIC_CALL:
    return InstructionType::Call;
  case ZYDIS_MNEMONIC_RET:
    return InstructionType::Ret;
  case ZYDIS_MNEMONIC_PUSH:
    return InstructionType::Push;
  case ZYDIS_MNEMONIC_POP:
    return InstructionType::Pop;
  case ZYDIS_MNEMONIC_PUSHA:
  case ZYDIS_MNEMONIC_PUSHAD:
    return InstructionType::Pusha;
  case ZYDIS_MNEMONIC_POPA:
  case ZYDIS_MNEMONIC_POPAD:
    return InstructionType::Popa;
  case ZYDIS_MNEMONIC_PUSHF:
    return InstructionType::Pushf;
  case ZYDIS_MNEMONIC_PUSHFD:
    return InstructionType::Pushfd;
  case ZYDIS_MNEMONIC_PUSHFQ:
    return InstructionType::Pushfq;
  case ZYDIS_MNEMONIC_POPF:
    return InstructionType::Popf;
  case ZYDIS_MNEMONIC_POPFD:
    return InstructionType::Popfd;
  case ZYDIS_MNEMONIC_POPFQ:
    return InstructionType::Popfq;
  case ZYDIS_MNEMONIC_LEA:
    return InstructionType::Lea;
  case ZYDIS_MNEMONIC_INC:
    return InstructionType::Inc;
  case ZYDIS_MNEMONIC_DEC:
    return InstructionType::Dec;
  case ZYDIS_MNEMONIC_SHL:
    return InstructionType::Shl;
  case ZYDIS_MNEMONIC_SHR:
    return InstructionType::Shr;
  case ZYDIS_MNEMONIC_SAR:
    return InstructionType::Sar;
  case ZYDIS_MNEMONIC_ROL:
    return InstructionType::Rol;
  case ZYDIS_MNEMONIC_ROR:
    return InstructionType::Ror;
  case ZYDIS_MNEMONIC_SYSCALL:
    return InstructionType::Syscall;
  case ZYDIS_MNEMONIC_SYSRET:
    return InstructionType::Sysret;
  case ZYDIS_MNEMONIC_INT:
    return InstructionType::Int;
  case ZYDIS_MNEMONIC_IRET:
  case ZYDIS_MNEMONIC_IRETD:
  case ZYDIS_MNEMONIC_IRETQ:
    return InstructionType::Iret;
  case ZYDIS_MNEMONIC_HLT:
    return InstructionType::Hlt;
  case ZYDIS_MNEMONIC_CLI:
    return InstructionType::Cli;
  case ZYDIS_MNEMONIC_STI:
    return InstructionType::Sti;
  case ZYDIS_MNEMONIC_CLD:
    return InstructionType::Cld;
  case ZYDIS_MNEMONIC_STD:
    return InstructionType::Std;
  case ZYDIS_MNEMONIC_ADC:
    return InstructionType::Adc;
  case ZYDIS_MNEMONIC_SBB:
    return InstructionType::Sbb;
  case ZYDIS_MNEMONIC_XCHG:
    return InstructionType::Xchg;
  case ZYDIS_MNEMONIC_MOVSX:
    return InstructionType::Movsx;
  case ZYDIS_MNEMONIC_MOVZX:
    return InstructionType::Movzx;

  // FPU Instructions (x87)
  case ZYDIS_MNEMONIC_FADD:
  case ZYDIS_MNEMONIC_FADDP:
    return InstructionType::Fadd;
  case ZYDIS_MNEMONIC_FSUB:
  case ZYDIS_MNEMONIC_FSUBP:
  case ZYDIS_MNEMONIC_FSUBR:
  case ZYDIS_MNEMONIC_FSUBRP:
    return InstructionType::Fsub;
  case ZYDIS_MNEMONIC_FMUL:
  case ZYDIS_MNEMONIC_FMULP:
    return InstructionType::Fmul;
  case ZYDIS_MNEMONIC_FDIV:
  case ZYDIS_MNEMONIC_FDIVP:
  case ZYDIS_MNEMONIC_FDIVR:
  case ZYDIS_MNEMONIC_FDIVRP:
    return InstructionType::Fdiv;
  case ZYDIS_MNEMONIC_FSQRT:
    return InstructionType::Fsqrt;
  case ZYDIS_MNEMONIC_FCOM:
  case ZYDIS_MNEMONIC_FCOMP:
  case ZYDIS_MNEMONIC_FCOMPP:
    return InstructionType::Fcom;
  case ZYDIS_MNEMONIC_FLD:
    return InstructionType::Fld;
  case ZYDIS_MNEMONIC_FST:
  case ZYDIS_MNEMONIC_FSTP:
    return InstructionType::Fst;
  case ZYDIS_MNEMONIC_FCHS:
    return InstructionType::Fchs;
  case ZYDIS_MNEMONIC_FABS:
    return InstructionType::Fabs;
  case ZYDIS_MNEMONIC_FCOS:
    return InstructionType::Fcos;
  case ZYDIS_MNEMONIC_FSIN:
    return InstructionType::Fsin;
  case ZYDIS_MNEMONIC_FPATAN:
    return InstructionType::Fpatan;
  case ZYDIS_MNEMONIC_F2XM1:
    return InstructionType::F2xm1;
  case ZYDIS_MNEMONIC_FYL2X:
    return InstructionType::Fyl2x;
  case ZYDIS_MNEMONIC_FYL2XP1:
    return InstructionType::Fyl2xp1;
  case ZYDIS_MNEMONIC_FSCALE:
    return InstructionType::Fscale;
  case ZYDIS_MNEMONIC_FPREM:
    return InstructionType::Fprem;
  case ZYDIS_MNEMONIC_FPREM1:
    return InstructionType::Fprem1;
  case ZYDIS_MNEMONIC_FXCH:
    return InstructionType::Fxch;
  case ZYDIS_MNEMONIC_FNOP:
    return InstructionType::Fnop;
  case ZYDIS_MNEMONIC_FNCLEX:
    return InstructionType::Fclex;
  case ZYDIS_MNEMONIC_FNINIT:
    return InstructionType::Finit;
  case ZYDIS_MNEMONIC_FWAIT:
    return InstructionType::Fwait;

  // SSE/SSE2/SSE3/SSSE3/SSE4.1/SSE4.2/AVX/AVX2/AVX512 Instructions
  case ZYDIS_MNEMONIC_MOVAPS:
    return InstructionType::Movaps;
  case ZYDIS_MNEMONIC_MOVUPS:
    return InstructionType::Movups;
  case ZYDIS_MNEMONIC_MOVAPD:
    return InstructionType::Movapd;
  case ZYDIS_MNEMONIC_MOVUPD:
    return InstructionType::Movupd;
  case ZYDIS_MNEMONIC_MOVSS:
    return InstructionType::Movss;
  case ZYDIS_MNEMONIC_MOVSD:
    // Distinguish between string operation and SSE operation
    // String MOVSD has no operands or implicit memory operands
    // SSE MOVSD has XMM register operands
    return InstructionType::Movsd_sse; // Default to SSE, will be corrected in
                                       // ConvertZydisOperand if needed
  case ZYDIS_MNEMONIC_ADDPS:
    return InstructionType::Addps;
  case ZYDIS_MNEMONIC_SUBPS:
    return InstructionType::Subps;
  case ZYDIS_MNEMONIC_MULPS:
    return InstructionType::Mulps;
  case ZYDIS_MNEMONIC_DIVPS:
    return InstructionType::Divps;
  case ZYDIS_MNEMONIC_ADDSS:
    return InstructionType::Addss;
  case ZYDIS_MNEMONIC_SUBSS:
    return InstructionType::Subss;
  case ZYDIS_MNEMONIC_MULSS:
    return InstructionType::Mulss;
  case ZYDIS_MNEMONIC_DIVSS:
    return InstructionType::Divss;
  case ZYDIS_MNEMONIC_ADDSD:
    return InstructionType::Addsd;
  case ZYDIS_MNEMONIC_SUBSD:
    return InstructionType::Subsd;
  case ZYDIS_MNEMONIC_MULSD:
    return InstructionType::Mulsd;
  case ZYDIS_MNEMONIC_DIVSD:
    return InstructionType::Divsd;
  case ZYDIS_MNEMONIC_SQRTPS:
    return InstructionType::Sqrtps;
  case ZYDIS_MNEMONIC_SQRTSD:
    return InstructionType::Sqrtsd;
  case ZYDIS_MNEMONIC_SQRTSS:
    return InstructionType::Sqrtss;
  case ZYDIS_MNEMONIC_ANDPS:
    return InstructionType::Andps;
  case ZYDIS_MNEMONIC_ORPS:
    return InstructionType::Orps;
  case ZYDIS_MNEMONIC_XORPS:
    return InstructionType::Xorps;
  case ZYDIS_MNEMONIC_ANDPD:
    return InstructionType::Andpd;
  case ZYDIS_MNEMONIC_ORPD:
    return InstructionType::Orpd;
  case ZYDIS_MNEMONIC_XORPD:
    return InstructionType::Xorpd;
  case ZYDIS_MNEMONIC_PADDQ:
    return InstructionType::Paddq;
  case ZYDIS_MNEMONIC_PSUBQ:
    return InstructionType::Psubq;
  case ZYDIS_MNEMONIC_PMULLD:
    return InstructionType::Pmulld;
  case ZYDIS_MNEMONIC_PADDD:
    return InstructionType::Paddd;
  case ZYDIS_MNEMONIC_PSUBD:
    return InstructionType::Psubd;
  case ZYDIS_MNEMONIC_PADDW:
    return InstructionType::Paddw;
  case ZYDIS_MNEMONIC_PSUBW:
    return InstructionType::Psubw;
  case ZYDIS_MNEMONIC_PADDB:
    return InstructionType::Paddb;
  case ZYDIS_MNEMONIC_PSUBB:
    return InstructionType::Psubb;
  case ZYDIS_MNEMONIC_PCMPEQB:
    return InstructionType::Pcmpeqb;
  case ZYDIS_MNEMONIC_PCMPEQW:
    return InstructionType::Pcmpeqw;
  case ZYDIS_MNEMONIC_PCMPEQD:
    return InstructionType::Pcmpeqd;
  case ZYDIS_MNEMONIC_PCMPEQQ:
    return InstructionType::Pcmpeqq;
  case ZYDIS_MNEMONIC_PMAXSW:
    return InstructionType::Pmaxsw;
  case ZYDIS_MNEMONIC_PMINSW:
    return InstructionType::Pminsw;
  case ZYDIS_MNEMONIC_PSHUFD:
    return InstructionType::Pshufd;
  case ZYDIS_MNEMONIC_UNPCKLPS:
    return InstructionType::Unpcklps;
  case ZYDIS_MNEMONIC_UNPCKHPS:
    return InstructionType::Unpckhps;
  case ZYDIS_MNEMONIC_SHUFPS:
    return InstructionType::Shufps;
  case ZYDIS_MNEMONIC_CVTSI2SS:
    return InstructionType::Cvtsi2ss;
  case ZYDIS_MNEMONIC_CVTSS2SI:
    return InstructionType::Cvtss2si;
  case ZYDIS_MNEMONIC_CVTSI2SD:
    return InstructionType::Cvtsi2sd;
  case ZYDIS_MNEMONIC_CVTSD2SI:
    return InstructionType::Cvtsd2si;
  case ZYDIS_MNEMONIC_CVTTSS2SI:
    return InstructionType::Cvttss2si;
  case ZYDIS_MNEMONIC_CVTTSD2SI:
    return InstructionType::Cvttsd2si;
  case ZYDIS_MNEMONIC_COMISS:
    return InstructionType::Comiss;
  case ZYDIS_MNEMONIC_COMISD:
    return InstructionType::Comisd;
  case ZYDIS_MNEMONIC_UCOMISS:
    return InstructionType::Ucomiss;
  case ZYDIS_MNEMONIC_UCOMISD:
    return InstructionType::Ucomisd;

  // AVX/AVX2 Instructions
  case ZYDIS_MNEMONIC_VMOVAPS:
    return InstructionType::Vmovaps;
  case ZYDIS_MNEMONIC_VMOVUPS:
    return InstructionType::Vmovups;
  case ZYDIS_MNEMONIC_VADDPS:
    return InstructionType::Vaddps;
  case ZYDIS_MNEMONIC_VSUBPS:
    return InstructionType::Vsubps;
  case ZYDIS_MNEMONIC_VMULPS:
    return InstructionType::Vmulps;
  case ZYDIS_MNEMONIC_VDIVPS:
    return InstructionType::Vdivps;
  case ZYDIS_MNEMONIC_VFMADD132PS:
    return InstructionType::Vfmadd132ps;
  case ZYDIS_MNEMONIC_VFMADD213PS:
    return InstructionType::Vfmadd213ps;
  case ZYDIS_MNEMONIC_VFMADD231PS:
    return InstructionType::Vfmadd231ps;

  default:
    spdlog::warn("Unmapped Zydis mnemonic: {}", static_cast<int>(mnemonic));
    return InstructionType::Unknown;
  }
}

ConditionCode
InstructionDecoder::MapZydisToConditionCode(ZydisBranchType branchType) const {
  // For now, return a default condition code since the specific branch type
  // constants are not available in this Zydis version
  // TODO: Implement proper mapping when Zydis branch types are available
  spdlog::trace("Branch type mapping not implemented for type: {}",
                static_cast<int>(branchType));
  return ConditionCode::Always;
}

Register InstructionDecoder::MapZydisToRegister(ZydisRegister zydisReg) const {
  switch (zydisReg) {
  // 64-bit general purpose registers
  case ZYDIS_REGISTER_RAX:
    return Register::RAX;
  case ZYDIS_REGISTER_RCX:
    return Register::RCX;
  case ZYDIS_REGISTER_RDX:
    return Register::RDX;
  case ZYDIS_REGISTER_RBX:
    return Register::RBX;
  case ZYDIS_REGISTER_RSP:
    return Register::RSP;
  case ZYDIS_REGISTER_RBP:
    return Register::RBP;
  case ZYDIS_REGISTER_RSI:
    return Register::RSI;
  case ZYDIS_REGISTER_RDI:
    return Register::RDI;
  case ZYDIS_REGISTER_R8:
    return Register::R8;
  case ZYDIS_REGISTER_R9:
    return Register::R9;
  case ZYDIS_REGISTER_R10:
    return Register::R10;
  case ZYDIS_REGISTER_R11:
    return Register::R11;
  case ZYDIS_REGISTER_R12:
    return Register::R12;
  case ZYDIS_REGISTER_R13:
    return Register::R13;
  case ZYDIS_REGISTER_R14:
    return Register::R14;
  case ZYDIS_REGISTER_R15:
    return Register::R15;
  case ZYDIS_REGISTER_RIP:
    return Register::RIP;

  // 32-bit general purpose registers
  case ZYDIS_REGISTER_EAX:
    return Register::EAX;
  case ZYDIS_REGISTER_ECX:
    return Register::ECX;
  case ZYDIS_REGISTER_EDX:
    return Register::EDX;
  case ZYDIS_REGISTER_EBX:
    return Register::EBX;
  case ZYDIS_REGISTER_ESP:
    return Register::ESP;
  case ZYDIS_REGISTER_EBP:
    return Register::EBP;
  case ZYDIS_REGISTER_ESI:
    return Register::ESI;
  case ZYDIS_REGISTER_EDI:
    return Register::EDI;
  case ZYDIS_REGISTER_R8D:
    return Register::R8D;
  case ZYDIS_REGISTER_R9D:
    return Register::R9D;
  case ZYDIS_REGISTER_R10D:
    return Register::R10D;
  case ZYDIS_REGISTER_R11D:
    return Register::R11D;
  case ZYDIS_REGISTER_R12D:
    return Register::R12D;
  case ZYDIS_REGISTER_R13D:
    return Register::R13D;
  case ZYDIS_REGISTER_R14D:
    return Register::R14D;
  case ZYDIS_REGISTER_R15D:
    return Register::R15D;

  // 16-bit general purpose registers
  case ZYDIS_REGISTER_AX:
    return Register::AX;
  case ZYDIS_REGISTER_CX:
    return Register::CX;
  case ZYDIS_REGISTER_DX:
    return Register::DX;
  case ZYDIS_REGISTER_BX:
    return Register::BX;
  case ZYDIS_REGISTER_SP:
    return Register::SP;
  case ZYDIS_REGISTER_BP:
    return Register::BP;
  case ZYDIS_REGISTER_SI:
    return Register::SI;
  case ZYDIS_REGISTER_DI:
    return Register::DI;
  case ZYDIS_REGISTER_R8W:
    return Register::R8W;
  case ZYDIS_REGISTER_R9W:
    return Register::R9W;
  case ZYDIS_REGISTER_R10W:
    return Register::R10W;
  case ZYDIS_REGISTER_R11W:
    return Register::R11W;
  case ZYDIS_REGISTER_R12W:
    return Register::R12W;
  case ZYDIS_REGISTER_R13W:
    return Register::R13W;
  case ZYDIS_REGISTER_R14W:
    return Register::R14W;
  case ZYDIS_REGISTER_R15W:
    return Register::R15W;

  // 8-bit general purpose registers (high and low bytes)
  case ZYDIS_REGISTER_AL:
    return Register::AL;
  case ZYDIS_REGISTER_CL:
    return Register::CL;
  case ZYDIS_REGISTER_DL:
    return Register::DL;
  case ZYDIS_REGISTER_BL:
    return Register::BL;
  case ZYDIS_REGISTER_AH:
    return Register::AH;
  case ZYDIS_REGISTER_CH:
    return Register::CH;
  case ZYDIS_REGISTER_DH:
    return Register::DH;
  case ZYDIS_REGISTER_BH:
    return Register::BH;
  case ZYDIS_REGISTER_SPL:
    return Register::SPL;
  case ZYDIS_REGISTER_BPL:
    return Register::BPL;
  case ZYDIS_REGISTER_SIL:
    return Register::SIL;
  case ZYDIS_REGISTER_DIL:
    return Register::DIL;
  case ZYDIS_REGISTER_R8B:
    return Register::R8B;
  case ZYDIS_REGISTER_R9B:
    return Register::R9B;
  case ZYDIS_REGISTER_R10B:
    return Register::R10B;
  case ZYDIS_REGISTER_R11B:
    return Register::R11B;
  case ZYDIS_REGISTER_R12B:
    return Register::R12B;
  case ZYDIS_REGISTER_R13B:
    return Register::R13B;
  case ZYDIS_REGISTER_R14B:
    return Register::R14B;
  case ZYDIS_REGISTER_R15B:
    return Register::R15B;

  // Segment registers
  case ZYDIS_REGISTER_ES:
    return Register::ES;
  case ZYDIS_REGISTER_CS:
    return Register::CS;
  case ZYDIS_REGISTER_SS:
    return Register::SS;
  case ZYDIS_REGISTER_DS:
    return Register::DS;
  case ZYDIS_REGISTER_FS:
    return Register::FS;
  case ZYDIS_REGISTER_GS:
    return Register::GS;

  // Control registers
  case ZYDIS_REGISTER_CR0:
    return Register::CR0;
  case ZYDIS_REGISTER_CR1:
    return Register::CR1;
  case ZYDIS_REGISTER_CR2:
    return Register::CR2;
  case ZYDIS_REGISTER_CR3:
    return Register::CR3;
  case ZYDIS_REGISTER_CR4:
    return Register::CR4;
  case ZYDIS_REGISTER_CR5:
    return Register::CR5;
  case ZYDIS_REGISTER_CR6:
    return Register::CR6;
  case ZYDIS_REGISTER_CR7:
    return Register::CR7;
  case ZYDIS_REGISTER_CR8:
    return Register::CR8;

  // Debug registers
  case ZYDIS_REGISTER_DR0:
    return Register::DR0;
  case ZYDIS_REGISTER_DR1:
    return Register::DR1;
  case ZYDIS_REGISTER_DR2:
    return Register::DR2;
  case ZYDIS_REGISTER_DR3:
    return Register::DR3;
  case ZYDIS_REGISTER_DR4:
    return Register::DR4;
  case ZYDIS_REGISTER_DR5:
    return Register::DR5;
  case ZYDIS_REGISTER_DR6:
    return Register::DR6;
  case ZYDIS_REGISTER_DR7:
    return Register::DR7;

  // MMX registers
  case ZYDIS_REGISTER_MM0:
    return Register::MM0;
  case ZYDIS_REGISTER_MM1:
    return Register::MM1;
  case ZYDIS_REGISTER_MM2:
    return Register::MM2;
  case ZYDIS_REGISTER_MM3:
    return Register::MM3;
  case ZYDIS_REGISTER_MM4:
    return Register::MM4;
  case ZYDIS_REGISTER_MM5:
    return Register::MM5;
  case ZYDIS_REGISTER_MM6:
    return Register::MM6;
  case ZYDIS_REGISTER_MM7:
    return Register::MM7;

  // XMM registers
  case ZYDIS_REGISTER_XMM0:
    return Register::XMM0;
  case ZYDIS_REGISTER_XMM1:
    return Register::XMM1;
  case ZYDIS_REGISTER_XMM2:
    return Register::XMM2;
  case ZYDIS_REGISTER_XMM3:
    return Register::XMM3;
  case ZYDIS_REGISTER_XMM4:
    return Register::XMM4;
  case ZYDIS_REGISTER_XMM5:
    return Register::XMM5;
  case ZYDIS_REGISTER_XMM6:
    return Register::XMM6;
  case ZYDIS_REGISTER_XMM7:
    return Register::XMM7;
  case ZYDIS_REGISTER_XMM8:
    return Register::XMM8;
  case ZYDIS_REGISTER_XMM9:
    return Register::XMM9;
  case ZYDIS_REGISTER_XMM10:
    return Register::XMM10;
  case ZYDIS_REGISTER_XMM11:
    return Register::XMM11;
  case ZYDIS_REGISTER_XMM12:
    return Register::XMM12;
  case ZYDIS_REGISTER_XMM13:
    return Register::XMM13;
  case ZYDIS_REGISTER_XMM14:
    return Register::XMM14;
  case ZYDIS_REGISTER_XMM15:
    return Register::XMM15;
  case ZYDIS_REGISTER_XMM16:
    return Register::XMM16;
  case ZYDIS_REGISTER_XMM17:
    return Register::XMM17;
  case ZYDIS_REGISTER_XMM18:
    return Register::XMM18;
  case ZYDIS_REGISTER_XMM19:
    return Register::XMM19;
  case ZYDIS_REGISTER_XMM20:
    return Register::XMM20;
  case ZYDIS_REGISTER_XMM21:
    return Register::XMM21;
  case ZYDIS_REGISTER_XMM22:
    return Register::XMM22;
  case ZYDIS_REGISTER_XMM23:
    return Register::XMM23;
  case ZYDIS_REGISTER_XMM24:
    return Register::XMM24;
  case ZYDIS_REGISTER_XMM25:
    return Register::XMM25;
  case ZYDIS_REGISTER_XMM26:
    return Register::XMM26;
  case ZYDIS_REGISTER_XMM27:
    return Register::XMM27;
  case ZYDIS_REGISTER_XMM28:
    return Register::XMM28;
  case ZYDIS_REGISTER_XMM29:
    return Register::XMM29;
  case ZYDIS_REGISTER_XMM30:
    return Register::XMM30;
  case ZYDIS_REGISTER_XMM31:
    return Register::XMM31;

  // YMM registers
  case ZYDIS_REGISTER_YMM0:
    return Register::YMM0;
  case ZYDIS_REGISTER_YMM1:
    return Register::YMM1;
  case ZYDIS_REGISTER_YMM2:
    return Register::YMM2;
  case ZYDIS_REGISTER_YMM3:
    return Register::YMM3;
  case ZYDIS_REGISTER_YMM4:
    return Register::YMM4;
  case ZYDIS_REGISTER_YMM5:
    return Register::YMM5;
  case ZYDIS_REGISTER_YMM6:
    return Register::YMM6;
  case ZYDIS_REGISTER_YMM7:
    return Register::YMM7;
  case ZYDIS_REGISTER_YMM8:
    return Register::YMM8;
  case ZYDIS_REGISTER_YMM9:
    return Register::YMM9;
  case ZYDIS_REGISTER_YMM10:
    return Register::YMM10;
  case ZYDIS_REGISTER_YMM11:
    return Register::YMM11;
  case ZYDIS_REGISTER_YMM12:
    return Register::YMM12;
  case ZYDIS_REGISTER_YMM13:
    return Register::YMM13;
  case ZYDIS_REGISTER_YMM14:
    return Register::YMM14;
  case ZYDIS_REGISTER_YMM15:
    return Register::YMM15;
  case ZYDIS_REGISTER_YMM16:
    return Register::YMM16;
  case ZYDIS_REGISTER_YMM17:
    return Register::YMM17;
  case ZYDIS_REGISTER_YMM18:
    return Register::YMM18;
  case ZYDIS_REGISTER_YMM19:
    return Register::YMM19;
  case ZYDIS_REGISTER_YMM20:
    return Register::YMM20;
  case ZYDIS_REGISTER_YMM21:
    return Register::YMM21;
  case ZYDIS_REGISTER_YMM22:
    return Register::YMM22;
  case ZYDIS_REGISTER_YMM23:
    return Register::YMM23;
  case ZYDIS_REGISTER_YMM24:
    return Register::YMM24;
  case ZYDIS_REGISTER_YMM25:
    return Register::YMM25;
  case ZYDIS_REGISTER_YMM26:
    return Register::YMM26;
  case ZYDIS_REGISTER_YMM27:
    return Register::YMM27;
  case ZYDIS_REGISTER_YMM28:
    return Register::YMM28;
  case ZYDIS_REGISTER_YMM29:
    return Register::YMM29;
  case ZYDIS_REGISTER_YMM30:
    return Register::YMM30;
  case ZYDIS_REGISTER_YMM31:
    return Register::YMM31;

  // ZMM registers
  case ZYDIS_REGISTER_ZMM0:
    return Register::ZMM0;
  case ZYDIS_REGISTER_ZMM1:
    return Register::ZMM1;
  case ZYDIS_REGISTER_ZMM2:
    return Register::ZMM2;
  case ZYDIS_REGISTER_ZMM3:
    return Register::ZMM3;
  case ZYDIS_REGISTER_ZMM4:
    return Register::ZMM4;
  case ZYDIS_REGISTER_ZMM5:
    return Register::ZMM5;
  case ZYDIS_REGISTER_ZMM6:
    return Register::ZMM6;
  case ZYDIS_REGISTER_ZMM7:
    return Register::ZMM7;
  case ZYDIS_REGISTER_ZMM8:
    return Register::ZMM8;
  case ZYDIS_REGISTER_ZMM9:
    return Register::ZMM9;
  case ZYDIS_REGISTER_ZMM10:
    return Register::ZMM10;
  case ZYDIS_REGISTER_ZMM11:
    return Register::ZMM11;
  case ZYDIS_REGISTER_ZMM12:
    return Register::ZMM12;
  case ZYDIS_REGISTER_ZMM13:
    return Register::ZMM13;
  case ZYDIS_REGISTER_ZMM14:
    return Register::ZMM14;
  case ZYDIS_REGISTER_ZMM15:
    return Register::ZMM15;
  case ZYDIS_REGISTER_ZMM16:
    return Register::ZMM16;
  case ZYDIS_REGISTER_ZMM17:
    return Register::ZMM17;
  case ZYDIS_REGISTER_ZMM18:
    return Register::ZMM18;
  case ZYDIS_REGISTER_ZMM19:
    return Register::ZMM19;
  case ZYDIS_REGISTER_ZMM20:
    return Register::ZMM20;
  case ZYDIS_REGISTER_ZMM21:
    return Register::ZMM21;
  case ZYDIS_REGISTER_ZMM22:
    return Register::ZMM22;
  case ZYDIS_REGISTER_ZMM23:
    return Register::ZMM23;
  case ZYDIS_REGISTER_ZMM24:
    return Register::ZMM24;
  case ZYDIS_REGISTER_ZMM25:
    return Register::ZMM25;
  case ZYDIS_REGISTER_ZMM26:
    return Register::ZMM26;
  case ZYDIS_REGISTER_ZMM27:
    return Register::ZMM27;
  case ZYDIS_REGISTER_ZMM28:
    return Register::ZMM28;
  case ZYDIS_REGISTER_ZMM29:
    return Register::ZMM29;
  case ZYDIS_REGISTER_ZMM30:
    return Register::ZMM30;
  case ZYDIS_REGISTER_ZMM31:
    return Register::ZMM31;

  // FPU (x87) stack registers
  case ZYDIS_REGISTER_ST0:
    return Register::ST0;
  case ZYDIS_REGISTER_ST1:
    return Register::ST1;
  case ZYDIS_REGISTER_ST2:
    return Register::ST2;
  case ZYDIS_REGISTER_ST3:
    return Register::ST3;
  case ZYDIS_REGISTER_ST4:
    return Register::ST4;
  case ZYDIS_REGISTER_ST5:
    return Register::ST5;
  case ZYDIS_REGISTER_ST6:
    return Register::ST6;
  case ZYDIS_REGISTER_ST7:
    return Register::ST7;

  // Other special registers (e.g., FLAGS, GDTR, LDTR, TR, MSR, TSC, etc.)
  case ZYDIS_REGISTER_EFLAGS:
    return Register::EFLAGS;
  case ZYDIS_REGISTER_RFLAGS:
    return Register::RFLAGS;
  case ZYDIS_REGISTER_MXCSR:
    return Register::MXCSR;
  case ZYDIS_REGISTER_K0:
    return Register::K0; // AVX512 Mask registers
  case ZYDIS_REGISTER_K1:
    return Register::K1;
  case ZYDIS_REGISTER_K2:
    return Register::K2;
  case ZYDIS_REGISTER_K3:
    return Register::K3;
  case ZYDIS_REGISTER_K4:
    return Register::K4;
  case ZYDIS_REGISTER_K5:
    return Register::K5;
  case ZYDIS_REGISTER_K6:
    return Register::K6;
  case ZYDIS_REGISTER_K7:
    return Register::K7;

  default:
    spdlog::warn("Unmapped Zydis register: {}", static_cast<int>(zydisReg));
    return Register::NONE;
  }
}

void InstructionDecoder::ConvertZydisOperand(
    const ZydisDecodedOperand &zydisOp,
    DecodedInstruction::Operand &ourOp) const {
  ourOp.reset();

  switch (zydisOp.type) {
  case ZYDIS_OPERAND_TYPE_REGISTER:
    if (zydisOp.reg.value >= ZYDIS_REGISTER_XMM0 &&
        zydisOp.reg.value <= ZYDIS_REGISTER_XMM31) {
      ourOp.type = DecodedInstruction::Operand::Type::XMM;
    } else if (zydisOp.reg.value >= ZYDIS_REGISTER_YMM0 &&
               zydisOp.reg.value <= ZYDIS_REGISTER_YMM31) {
      ourOp.type = DecodedInstruction::Operand::Type::YMM;
    } else if (zydisOp.reg.value >= ZYDIS_REGISTER_ZMM0 &&
               zydisOp.reg.value <= ZYDIS_REGISTER_ZMM31) {
      ourOp.type = DecodedInstruction::Operand::Type::ZMM;
    } else if (zydisOp.reg.value >= ZYDIS_REGISTER_MM0 &&
               zydisOp.reg.value <= ZYDIS_REGISTER_MM7) {
      ourOp.type = DecodedInstruction::Operand::Type::MMX;
    } else if (zydisOp.reg.value >= ZYDIS_REGISTER_ST0 &&
               zydisOp.reg.value <= ZYDIS_REGISTER_ST7) {
      ourOp.type = DecodedInstruction::Operand::Type::ST;
    } else if (zydisOp.reg.value >= ZYDIS_REGISTER_ES &&
               zydisOp.reg.value <= ZYDIS_REGISTER_GS) {
      ourOp.type = DecodedInstruction::Operand::Type::SEGMENT;
    } else if (zydisOp.reg.value >= ZYDIS_REGISTER_CR0 &&
               zydisOp.reg.value <= ZYDIS_REGISTER_CR8) {
      ourOp.type = DecodedInstruction::Operand::Type::CONTROL;
    } else if (zydisOp.reg.value >= ZYDIS_REGISTER_DR0 &&
               zydisOp.reg.value <= ZYDIS_REGISTER_DR7) {
      ourOp.type = DecodedInstruction::Operand::Type::DEBUG;
    } else {
      ourOp.type = DecodedInstruction::Operand::Type::REGISTER;
    }
    ourOp.reg = MapZydisToRegister(zydisOp.reg.value);
    ourOp.size = zydisOp.size * 8; // Convert bytes to bits
    break;

  case ZYDIS_OPERAND_TYPE_MEMORY:
    ourOp.type = DecodedInstruction::Operand::Type::MEMORY;
    ourOp.memory.base = MapZydisToRegister(zydisOp.mem.base);
    ourOp.memory.index = MapZydisToRegister(zydisOp.mem.index);
    ourOp.memory.scale = zydisOp.mem.scale;
    ourOp.memory.displacement = zydisOp.mem.disp.value;
    ourOp.size = zydisOp.size * 8; // Convert bytes to bits
    break;

  case ZYDIS_OPERAND_TYPE_IMMEDIATE:
    ourOp.type = DecodedInstruction::Operand::Type::IMMEDIATE;
    if (zydisOp.imm.is_signed) {
      ourOp.immediate = static_cast<uint64_t>(zydisOp.imm.value.s);
    } else {
      ourOp.immediate = zydisOp.imm.value.u;
    }
    ourOp.size = zydisOp.size * 8; // Convert bytes to bits
    break;

  case ZYDIS_OPERAND_TYPE_POINTER:
    // Handle far pointers if needed
    ourOp.type =
        DecodedInstruction::Operand::Type::IMMEDIATE; // Treat as immediate
                                                      // target for now
    ourOp.immediate = zydisOp.ptr.offset;
    ourOp.size = zydisOp.size * 8;
    break;

  default:
    ourOp.type = DecodedInstruction::Operand::Type::NONE;
    break;
  }
}

const std::unordered_map<InstructionType, uint64_t> &
InstructionDecoder::GetStats() const {
  return stats;
}

void InstructionDecoder::ResetStats() { stats.clear(); }

} // namespace x86_64
