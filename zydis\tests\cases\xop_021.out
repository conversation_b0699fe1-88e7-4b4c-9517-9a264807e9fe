== [    BASIC ] ============================================================================================
   MNEMONIC: vpcomud [ENC: XOP, MAP: XOP8, OPC: 0xEE]
     LENGTH: 10
        SSZ: 64
       EOSZ: 32
       EASZ: 64
   CATEGORY: XOP
    ISA-SET: XOP
    ISA-EXT: XOP
 EXCEPTIONS: NONE
 ATTRIBUTES: HAS_MODRM HAS_XOP ACCEPTS_SEGMENT 
  OPTIMIZED: 8F 68 00 EE B2 F0 06 01 49 FA 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    128      4      32      UINT                        xmm14
 1   REGISTER    EXPLICIT       R        NDSNDD    128      4      32      UINT                        xmm15
 2     MEMORY    EXPLICIT       R      MODRM_RM    128      4      32      UINT  TYPE  =                 MEM
                                                                                 SEG   =                  ds
                                                                                 BASE  =                 rdx
                                                                                 INDEX =                none
                                                                                 SCALE =                   0
                                                                                 DISP  =  0x00000000490106F0
 3  IMMEDIATE    EXPLICIT       R         UIMM8      8      1       8      UINT  [U A  8] 0x00000000000000FA
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 128
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vpcomud $0xFA, 0x490106F0(%rdx), %xmm15, %xmm14
   RELATIVE: vpcomud $0xFA, 0x490106F0(%rdx), %xmm15, %xmm14

== [    INTEL ] ============================================================================================
   ABSOLUTE: vpcomud xmm14, xmm15, xmmword ptr ds:[rdx+0x490106F0], 0xFA
   RELATIVE: vpcomud xmm14, xmm15, xmmword ptr ds:[rdx+0x490106F0], 0xFA

== [ SEGMENTS ] ============================================================================================
8F 28 00 EE B2 F0 06 01 49 FA 
:        :  :  :           :..IMM
:        :  :  :..DISP
:        :  :..MODRM
:        :..OPCODE
:..XOP
