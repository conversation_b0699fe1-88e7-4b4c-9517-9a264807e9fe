== [    BASIC ] ============================================================================================
   MNEMONIC: vgminpd [ENC: MVEX, MAP: 0F38, OPC: 0x52]
     LENGTH:  8
        SSZ: 64
       EOSZ: 64
       EASZ: 64
   CATEGORY: KNC
    ISA-SET: KNCE
    ISA-EXT: KNCE
 EXCEPTIONS: NONE
 ATTRIBUTES: HAS_MODRM HAS_SIB HAS_MVEX ACCEPTS_SEGMENT 
  OPTIMIZED: 62 02 B1 20 52 6C 83 BE 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>     RCW     MODRM_REG    512      8      64   FLOAT64                        zmm29
 1   REGISTER    EXPLICIT       R          MASK     16     16       1       INT                           k0
 2   REGISTER    EXPLICIT       R        NDSNDD    512      8      64   FLOAT64                        zmm25
 3     MEMORY    EXPLICIT       R      MODRM_RM    256      4      64   FLOAT64  TYPE  =                 MEM
                                                                                 SEG   =                  ds
                                                                                 BASE  =                 r11
                                                                                 INDEX =                  r8
                                                                                 SCALE =                   4
                                                                                 DISP  =  0xFFFFFFFFFFFFF7C0
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 512
  BROADCAST: 4_TO_8
   ROUNDING: NONE
        SAE: N
       MASK: k0 [MERGING]
         EH: N
    SWIZZLE: NONE
    CONVERT: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vgminpd -0x840(%r11,%r8,4) {4to8}, %zmm25, %zmm29
   RELATIVE: vgminpd -0x840(%r11,%r8,4) {4to8}, %zmm25, %zmm29

== [    INTEL ] ============================================================================================
   ABSOLUTE: vgminpd zmm29, zmm25, ymmword ptr ds:[r11+r8*4-0x840] {4to8}
   RELATIVE: vgminpd zmm29, zmm25, ymmword ptr ds:[r11+r8*4-0x840] {4to8}

== [ SEGMENTS ] ============================================================================================
62 02 B1 20 52 6C 83 BE 
:           :  :  :  :..DISP
:           :  :  :..SIB
:           :  :..MODRM
:           :..OPCODE
:..MVEX
