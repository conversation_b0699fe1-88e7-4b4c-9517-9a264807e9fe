== [    BASIC ] ============================================================================================
   MNEMONIC: vpshlw [ENC: XOP, MAP: XOP9, OPC: 0x95]
     LENGTH:  5
        SSZ: 64
       EOSZ: 64
       EASZ: 64
   CATEGORY: XOP
    ISA-SET: XOP
    ISA-EXT: XOP
 EXCEPTIONS: NONE
 ATTRIBUTES: HAS_MODRM HAS_XOP ACCEPTS_SEGMENT 
  OPTIMIZED: 8F 49 88 95 00 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    128      8      16      UINT                         xmm8
 1   REGISTER    EXPLICIT       R        NDSNDD    128      8      16      UINT                        xmm14
 2     MEMORY    EXPLICIT       R      MODRM_RM    128      8      16      UINT  TYPE  =                 MEM
                                                                                 SEG   =                  ds
                                                                                 BASE  =                  r8
                                                                                 INDEX =                none
                                                                                 SCALE =                   0
                                                                                 DISP  =  0x0000000000000000
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 128
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vpshlw (%r8), %xmm14, %xmm8
   RELATIVE: vpshlw (%r8), %xmm14, %xmm8

== [    INTEL ] ============================================================================================
   ABSOLUTE: vpshlw xmm8, xmm14, xmmword ptr ds:[r8]
   RELATIVE: vpshlw xmm8, xmm14, xmmword ptr ds:[r8]

== [ SEGMENTS ] ============================================================================================
8F 49 88 95 00 
:        :  :..MODRM
:        :..OPCODE
:..XOP
