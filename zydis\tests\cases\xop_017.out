== [    BASIC ] ============================================================================================
   MNEMONIC: vpcomd [ENC: XOP, MAP: XOP8, OPC: 0xCE]
     LENGTH:  6
        SSZ: 64
       EOSZ: 32
       EASZ: 64
   CATEGORY: XOP
    ISA-SET: XOP
    ISA-EXT: XOP
 EXCEPTIONS: NONE
 ATTRIBUTES: HAS_MODRM HAS_XOP ACCEPTS_SEGMENT 
  OPTIMIZED: 8F C8 40 CE 31 13 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    128      4      32       INT                         xmm6
 1   REGISTER    EXPLICIT       R        NDSNDD    128      4      32       INT                         xmm7
 2     MEMORY    EXPLICIT       R      MODRM_RM    128      4      32       INT  TYPE  =                 MEM
                                                                                 SEG   =                  ds
                                                                                 BASE  =                  r9
                                                                                 INDEX =                none
                                                                                 SCALE =                   0
                                                                                 DISP  =  0x0000000000000000
 3  IMMEDIATE    EXPLICIT       R         UIMM8      8      1       8      UINT  [U A  8] 0x0000000000000013
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 128
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vpcomd $0x13, (%r9), %xmm7, %xmm6
   RELATIVE: vpcomd $0x13, (%r9), %xmm7, %xmm6

== [    INTEL ] ============================================================================================
   ABSOLUTE: vpcomd xmm6, xmm7, xmmword ptr ds:[r9], 0x13
   RELATIVE: vpcomd xmm6, xmm7, xmmword ptr ds:[r9], 0x13

== [ SEGMENTS ] ============================================================================================
8F C8 40 CE 31 13 
:        :  :  :..IMM
:        :  :..MODRM
:        :..OPCODE
:..XOP
