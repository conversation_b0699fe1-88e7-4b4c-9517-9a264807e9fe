== [    BASIC ] ============================================================================================
   MNEMONIC: vroundsd [ENC: VEX, MAP: 0F3A, OPC: 0x0B]
     LENGTH:  6
        SSZ: 64
       EOSZ: 32
       EASZ: 64
   CATEGORY: AVX
    ISA-SET: AVX
    ISA-EXT: AVX
 EXCEPTIONS: AVX3
 ATTRIBUTES: HAS_MODRM HAS_VEX 
  OPTIMIZED: C4 63 59 0B E6 AE 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    128      2      64   FLOAT64                        xmm12
 1   REGISTER    EXPLICIT       R        NDSNDD    128      2      64   FLOAT64                         xmm4
 2   REGISTER    EXPLICIT       R      MODRM_RM     64      1      64   FLOAT64                         xmm6
 3  IMMEDIATE    EXPLICIT       R         UIMM8      8      1       8      UINT  [U A  8] 0x00000000000000AE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 128
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vroundsd $0xAE, %xmm6, %xmm4, %xmm12
   RELATIVE: vroundsd $0xAE, %xmm6, %xmm4, %xmm12

== [    INTEL ] ============================================================================================
   ABSOLUTE: vroundsd xmm12, xmm4, xmm6, 0xAE
   RELATIVE: vroundsd xmm12, xmm4, xmm6, 0xAE

== [ SEGMENTS ] ============================================================================================
C4 23 59 0B E6 AE 
:        :  :  :..IMM
:        :  :..MODRM
:        :..OPCODE
:..VEX
