PROJECT_NAME           = Zycore
PROJECT_NUMBER         = @VERSION@
PROJECT_BRIEF          = "Zyan Core Library for C"
OUTPUT_DIRECTORY       = "@TOP_BUILDDIR@/doc"
STRIP_FROM_PATH        = "@TOP_SRCDIR@"
JAVADOC_AUTOBRIEF      = YES
QT_AUTOBRIEF           = YES
OPTIMIZE_OUTPUT_FOR_C  = YES
TOC_INCLUDE_HEADINGS   = 0
EXTRACT_ALL            = YES
EXTRACT_LOCAL_CLASSES  = NO
HIDE_SCOPE_NAMES       = YES
INPUT                  = "@TOP_SRCDIR@/include" \
                         "@TOP_SRCDIR@/README.md"
RECURSIVE              = YES
EXAMPLE_PATH           = "@TOP_SRCDIR@/examples"
USE_MDFILE_AS_MAINPAGE = "@TOP_SRCDIR@/README.md"
GENERATE_TREEVIEW      = YES
USE_MATHJAX            = YES
MATHJAX_VERSION        = MathJax_3
GENERATE_LATEX         = NO
MACRO_EXPANSION        = YES
PREDEFINED             = @PREDEFINED@
DOT_COMMON_ATTR        = "fontname=\"sans-serif\",fontsize=10"
DOT_EDGE_ATTR          = "labelfontname=\"sans-serif\",labelfontsize=10"
DOT_IMAGE_FORMAT       = svg
INTERACTIVE_SVG        = YES
HAVE_DOT               = @HAVE_DOT@
DOT_MULTI_TARGETS      = @HAVE_DOT_1_8_10@
DOT_PATH               = "@DOT_PATH@"
HTML_FORMULA_FORMAT    = @HTML_FORMULA_FORMAT@
