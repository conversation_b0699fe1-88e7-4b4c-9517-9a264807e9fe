== [    BASIC ] ============================================================================================
   MNEMONIC: vfnmsubps [ENC: VEX, MAP: 0F3A, OPC: 0x7C]
     LENGTH:  6
        SSZ: 64
       EOSZ: 32
       EASZ: 64
   CATEGORY: FMA4
    ISA-SET: FMA4
    ISA-EXT: FMA4
 EXCEPTIONS: NONE
 ATTRIBUTES: HAS_MODRM HAS_VEX ACCEPTS_SEGMENT 
  OPTIMIZED: C4 E3 05 7C 33 E0 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    256      8      32   FLOAT32                         ymm6
 1   REGISTER    EXPLICIT       R        NDSNDD    256      8      32   FLOAT32                        ymm15
 2     MEMORY    EXPLICIT       R      MODRM_RM    256      8      32   FLOAT32  TYPE  =                 MEM
                                                                                 SEG   =                  ds
                                                                                 BASE  =                 rbx
                                                                                 INDEX =                none
                                                                                 SCALE =                   0
                                                                                 DISP  =  0x0000000000000000
 3   REGISTER    EXPLICIT       R           IS4    256      8      32   FLOAT32                        ymm14
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 256
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vfnmsubps %ymm14, (%rbx), %ymm15, %ymm6
   RELATIVE: vfnmsubps %ymm14, (%rbx), %ymm15, %ymm6

== [    INTEL ] ============================================================================================
   ABSOLUTE: vfnmsubps ymm6, ymm15, ymmword ptr ds:[rbx], ymm14
   RELATIVE: vfnmsubps ymm6, ymm15, ymmword ptr ds:[rbx], ymm14

== [ SEGMENTS ] ============================================================================================
C4 A3 05 7C 33 E5 
:        :  :  :..IMM
:        :  :..MODRM
:        :..OPCODE
:..VEX
