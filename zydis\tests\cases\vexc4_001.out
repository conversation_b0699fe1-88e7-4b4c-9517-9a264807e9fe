== [    BASIC ] ============================================================================================
   MNEMONIC: vpcmpeqb [ENC: VEX, MAP: 0F, OPC: 0x74]
     LENGTH:  6
        SSZ: 64
       EOSZ: 32
       EASZ: 64
   CATEGORY: AVX2
    ISA-SET: AVX2
    ISA-EXT: AVX2
 EXCEPTIONS: AVX4
 ATTRIBUTES: HAS_MODRM HAS_VEX ACCEPTS_SEGMENT 
  OPTIMIZED: C4 C1 05 74 66 FD 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    256     32       8      UINT                         ymm4
 1   REGISTER    EXPLICIT       R        NDSNDD    256     32       8      UINT                        ymm15
 2     MEMORY    EXPLICIT       R      MODRM_RM    256     32       8      UINT  TYPE  =                 MEM
                                                                                 SEG   =                  ds
                                                                                 BASE  =                 r14
                                                                                 INDEX =                none
                                                                                 SCALE =                   0
                                                                                 DISP  =  0xFFFFFFFFFFFFFFFD
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 256
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vpcmpeqb -0x03(%r14), %ymm15, %ymm4
   RELATIVE: vpcmpeqb -0x03(%r14), %ymm15, %ymm4

== [    INTEL ] ============================================================================================
   ABSOLUTE: vpcmpeqb ymm4, ymm15, ymmword ptr ds:[r14-0x03]
   RELATIVE: vpcmpeqb ymm4, ymm15, ymmword ptr ds:[r14-0x03]

== [ SEGMENTS ] ============================================================================================
C4 C1 05 74 66 FD 
:        :  :  :..DISP
:        :  :..MODRM
:        :..OPCODE
:..VEX
