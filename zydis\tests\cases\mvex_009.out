== [    BASIC ] ============================================================================================
   MNEMONIC: vfnmsub213ps [ENC: MVEX, MAP: 0F38, OPC: 0xAE]
     LENGTH:  6
        SSZ: 64
       EOSZ: 32
       EASZ: 64
   CATEGORY: UFMA
    ISA-SET: KNCE
    ISA-EXT: KNCE
 EXCEPTIONS: NONE
 ATTRIBUTES: HAS_MODRM HAS_MVEX ACCEPTS_SEGMENT 
  OPTIMIZED: 62 72 59 3E AE 37 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>     RCW     MODRM_REG    512     16      32   FLOAT32                        zmm14
 1   REGISTER    EXPLICIT       R          MASK     16     16       1       INT                           k6
 2   REGISTER    EXPLICIT       R        NDSNDD    512     16      32   FLOAT32                         zmm4
 3     MEMORY    EXPLICIT       R      MODRM_RM    256     16      16   FLOAT16  TYPE  =                 MEM
                                                                                 SEG   =                  ds
                                                                                 BASE  =                 rdi
                                                                                 INDEX =                none
                                                                                 SCALE =                   0
                                                                                 DISP  =  0x0000000000000000
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 512
  BROADCAST: NONE
   ROUNDING: NONE
        SAE: N
       MASK: k6 [MERGING]
         EH: N
    SWIZZLE: NONE
    CONVERT: FLOAT16

== [      ATT ] ============================================================================================
   ABSOLUTE: vfnmsub213ps (%rdi) {float16}, %zmm4, %zmm14 {%k6}
   RELATIVE: vfnmsub213ps (%rdi) {float16}, %zmm4, %zmm14 {%k6}

== [    INTEL ] ============================================================================================
   ABSOLUTE: vfnmsub213ps zmm14 {k6}, zmm4, ymmword ptr ds:[rdi] {float16}
   RELATIVE: vfnmsub213ps zmm14 {k6}, zmm4, ymmword ptr ds:[rdi] {float16}

== [ SEGMENTS ] ============================================================================================
62 72 59 3E AE 37 
:           :  :..MODRM
:           :..OPCODE
:..MVEX
