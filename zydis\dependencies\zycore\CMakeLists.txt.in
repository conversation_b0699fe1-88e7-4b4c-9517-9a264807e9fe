cmake_minimum_required(VERSION 3.10 FATAL_ERROR)

project(googletest-download NONE)

include(ExternalProject)
ExternalProject_Add(googletest
  GIT_REPOSITORY    https://github.com/google/googletest.git
  GIT_TAG           v1.17.0
  SOURCE_DIR        "${CMAKE_BINARY_DIR}/gtest/src"
  BINARY_DIR        "${CMAKE_BINARY_DIR}/gtest/build"
  CONFIGURE_COMMAND ""
  BUILD_COMMAND     ""
  INSTALL_COMMAND   ""
  TEST_COMMAND      ""
)
