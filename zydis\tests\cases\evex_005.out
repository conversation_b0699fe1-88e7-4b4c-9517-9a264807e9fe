== [    BASIC ] ============================================================================================
   MNEMONIC: vfnmsub213pd [ENC: EVEX, MAP: 0F38, OPC: 0xAE]
     LENGTH:  6
        SSZ: 64
       EOSZ: 64
       EASZ: 64
   CATEGORY: VFMA
    ISA-SET: AVX512F_512
    ISA-EXT: AVX512EVEX
 EXCEPTIONS: E2
 ATTRIBUTES: HAS_MODRM HAS_EVEX 
  OPTIMIZED: 62 92 FD 48 AE D6 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>      RW     MODRM_REG    512      8      64   FLOAT64                         zmm2
 1   REGISTER    EXPLICIT       R          MASK     64     64       1       INT                           k0
 2   REGISTER    EXPLICIT       R        NDSNDD    512      8      64   FLOAT64                         zmm0
 3   REGISTER    EXPLICIT       R      MODRM_RM    512      8      64   FLOAT64                        zmm30
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 512
  BROADCAST: NONE
   ROUNDING: NONE
        SAE: N
       MASK: k0 [DISABLED]

== [      ATT ] ============================================================================================
   ABSOLUTE: vfnmsub213pd %zmm30, %zmm0, %zmm2
   RELATIVE: vfnmsub213pd %zmm30, %zmm0, %zmm2

== [    INTEL ] ============================================================================================
   ABSOLUTE: vfnmsub213pd zmm2, zmm0, zmm30
   RELATIVE: vfnmsub213pd zmm2, zmm0, zmm30

== [ SEGMENTS ] ============================================================================================
62 92 FD 48 AE D6 
:           :  :..MODRM
:           :..OPCODE
:..EVEX
