== [    BASIC ] ============================================================================================
   MNEMONIC: vshufpd [ENC: EVEX, MAP: 0F, OPC: 0xC6]
     LENGTH:  7
        SSZ: 64
       EOSZ: 64
       EASZ: 64
   CATEGORY: AVX512
    ISA-SET: AVX512F_512
    ISA-EXT: AVX512EVEX
 EXCEPTIONS: E4NF
 ATTRIBUTES: HAS_MODRM HAS_EVEX ACCEPTS_SEGMENT 
  OPTIMIZED: 62 F1 B5 DB C6 22 27 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    512      8      64   FLOAT64                         zmm4
 1   REGISTER    EXPLICIT       R          MASK     64     64       1       INT                           k3
 2   REGISTER    EXPLICIT       R        NDSNDD    512      8      64   FLOAT64                         zmm9
 3     MEMORY    EXPLICIT       R      MODRM_RM     64      1      64   FLOAT64  TYPE  =                 MEM
                                                                                 SEG   =                  ds
                                                                                 BASE  =                 rdx
                                                                                 INDEX =                none
                                                                                 SCALE =                   0
                                                                                 DISP  =  0x0000000000000000
 4  IMMEDIATE    EXPLICIT       R         UIMM8      8      1       8      UINT  [U A  8] 0x0000000000000027
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 512
  BROADCAST: 1_TO_8
   ROUNDING: NONE
        SAE: N
       MASK: k3 [ZEROING]

== [      ATT ] ============================================================================================
   ABSOLUTE: vshufpd $0x27, (%rdx) {1to8}, %zmm9, %zmm4 {%k3} {z}
   RELATIVE: vshufpd $0x27, (%rdx) {1to8}, %zmm9, %zmm4 {%k3} {z}

== [    INTEL ] ============================================================================================
   ABSOLUTE: vshufpd zmm4 {k3} {z}, zmm9, qword ptr ds:[rdx] {1to8}, 0x27
   RELATIVE: vshufpd zmm4 {k3} {z}, zmm9, qword ptr ds:[rdx] {1to8}, 0x27

== [ SEGMENTS ] ============================================================================================
62 B1 B5 DB C6 22 27 
:           :  :  :..IMM
:           :  :..MODRM
:           :..OPCODE
:..EVEX
