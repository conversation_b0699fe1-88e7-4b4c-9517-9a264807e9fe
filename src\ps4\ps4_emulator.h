// Copyright 2025 <Copyright Owner>

#pragma once

#include "../cpu/x86_64_cpu.h"
#include "../cpu/x86_64_pipeline.h"
#include "../emulator/apic.h"
#include "../emulator/interrupt_handler.h"
#include "../emulator/io_manager.h" // Include IOManager for x86_64::IOManager
#include "../jit/x86_64_jit_compiler.h"
#include "../loader/elf_loader.h"
#include "../loader/pkg_installer.h"
// Forward declaration to avoid circular include
#include "../memory/ps4_mmu.h"
#include "../memory/tlb.h"
#include "../ps4/fiber_manager.h"
#include "../ps4/orbis_os.h"
#include "../ps4/ps4_audio.h"
#include "../ps4/ps4_controllers.h"
#include "../ps4/ps4_filesystem.h"
#include "../ps4/ps4_gpu.h"
#include "../ps4/ps4_tsc.h"
#include "../ps4/trophy_manager.h"
#include "../ps4/zlib_wrapper.h"
#include "../syscall/syscall_handler.h"
#include "../video_core/command_processor.h"
#include "../video_core/gnm_shader_translator.h"
#include "../video_core/tile_manager.h"
#include <SDL.h>
#include <array>
#include <atomic>
#include <cstdint>
#include <filesystem>
#include <functional>
#include <memory>
#include <shared_mutex>
#include <stdexcept>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>


// Forward declarations for x86_64 namespace
namespace x86_64 {
class FiberManager;
class X86_64JITCompiler;
} // namespace x86_64

namespace ps4 {

// Forward declarations
struct LoadedElf;          // Defined in elf_loader.h
class OrbisOS;             // Defined in orbis_os.h
class PKGInstaller;        // Defined in pkg_installer.h
class PS4GPU;              // Defined in ps4_gpu.h
class EnhancedStats;       // Defined in enhanced_stats.h
class DeadlockDetector;    // Defined in deadlock_detector.h
class GNMShaderTranslator; // Defined in gnm_shader_translator.h
class CommandProcessor;    // Defined in command_processor.h
class TileManager;         // Defined in tile_manager.h

// External function to get audio enabled setting from main.cpp
extern bool IsAudioEnabled();

/**
 * @brief Exception for emulator-related errors.
 */
struct PS4EmulatorException : std::runtime_error {
  explicit PS4EmulatorException(const std::string &msg)
      : std::runtime_error(msg) {}
};

/**
 * @brief Exception for memory allocation failures.
 */
struct MemoryAllocationException : PS4EmulatorException {
  explicit MemoryAllocationException(const std::string &msg)
      : PS4EmulatorException("Memory allocation error: " + msg) {}
};

/**
 * @brief Structure representing a breakpoint.
 */
struct Breakpoint {
  uint64_t address;      ///< Breakpoint address
  bool enabled;          ///< Enabled state
  std::string condition; ///< Optional condition (e.g., register value)
};

/**
 * @brief Core emulator class for PS4 emulation.
 * @details Manages CPU, MMU, GPU, audio, controllers, and other components,
 * providing initialization, execution, and state management with
 * thread-safe operations.
 */
class PS4Emulator {
public:
  /**
   * @brief Constructs the emulator.
   */
  explicit PS4Emulator();

  /**
   * @brief Destructor, cleaning up resources.
   */
  ~PS4Emulator();

  /**
   * @brief Initializes the emulator.
   * @param window SDL window for rendering.
   * @param vulkanContext Shared Vulkan context.
   * @return True on success, false otherwise.
   */
  bool Initialize(SDL_Window *window, VulkanContext *vulkanContext);

  /**
   * @brief Shuts down the emulator, releasing resources.
   */
  void Shutdown();

  /**
   * @brief Loads a game executable.
   * @param path Path to the executable.
   * @return True on success, false otherwise.
   */
  bool LoadGame(const std::string &path);

  /**
   * @brief Loads an installed game by content ID.
   * @param contentId Content ID of the installed game.
   * @return True on success, false otherwise.
   */
  bool LoadInstalledGame(const std::string &contentId);

  /**
   * @brief Starts the emulator execution.
   */
  void Start();

  /**
   * @brief Pauses the emulator execution.
   */
  void Pause();

  /**
   * @brief Stops the emulator execution.
   */
  void Stop();

  /**
   * @brief Resumes the emulator execution.
   */
  void Resume();

  /**
   * @brief Resets the emulator state.
   */
  void Reset();

  /**
   * @brief Adds a breakpoint at an address.
   * @param address Breakpoint address.
   * @param condition Optional condition (e.g., "rax==0").
   */
  void AddBreakpoint(uint64_t address, const std::string &condition = "");

  /**
   * @brief Removes a breakpoint at an address.
   * @param address Breakpoint address.
   */
  void RemoveBreakpoint(uint64_t address);

  /**
   * @brief Adds a loaded module.
   * @param name Module name.
   * @param moduleId Module ID.
   */
  void AddLoadedModule(const std::string &name, const int moduleId);

  /**
   * @brief Gets a CPU instance by index.
   * @param index CPU index.
   * @return Reference to the CPU.
   */
  x86_64::X86_64CPU &GetCPU(uint32_t index) { return *m_cpus[index]; }
  /**
   * @brief Gets the number of CPU cores.
   * @return Number of CPUs.
   */
  uint32_t GetCPUCount() const { return static_cast<uint32_t>(m_cpus.size()); }

  /**
   * @brief Sets the entry point for all CPU cores.
   * @param entryPoint The entry point address for execution.
   */
  void SetCPUEntryPoints(uint64_t entryPoint);

  /**
   * @brief Gets the memory instance.
   * @return Reference to the memory.
   */
  PS4MMU &GetMemory() { return *m_mmu; }

  /**
   * @brief Gets the MMU instance.
   * @return Reference to the MMU.
   */
  PS4MMU &GetMMU() { return *m_mmu; }

  /**
   * @brief Gets the GPU instance.
   * @return Reference to the GPU.
   */
  PS4GPU &GetGPU() { return *m_gpu; }

  /**
   * @brief Gets the TSC instance.
   * @return Reference to the TSC.
   */
  PS4TSC &GetTSC() { return *m_tsc; }

  /**
   * @brief Gets the OrbisOS instance.
   * @return Reference to the OS.
   */
  OrbisOS &GetOrbisOS() { return *m_os; }

  /**
   * @brief Gets the filesystem instance.
   * @return Reference to the filesystem.
   */
  PS4Filesystem &GetFilesystem() { return *m_filesystem; }

  /**
   * @brief Gets the interrupt handler instance.
   * @return Reference to the interrupt handler.
   */
  x86_64::InterruptHandler &GetInterruptHandler() {
    return *m_interruptHandler;
  }

  /**
   * @brief Gets the I/O manager instance.
   * @return Reference to the I/O manager.
   */
  x86_64::IOManager &GetIOManager() { return *m_ioManager; }

  /**
   * @brief Gets the fiber manager instance.
   * @return Reference to the fiber manager.
   */
  FiberManager &GetFiberManager() { return *m_fiberManager; }

  /**
   * @brief Gets the trophy manager instance.
   * @return Reference to the trophy manager.
   */
  TrophyManager &GetTrophyManager() { return *m_trophyManager; }

  /**
   * @brief Gets the zlib wrapper instance.
   * @return Reference to the zlib wrapper.
   */
  ZlibWrapper &GetZlibWrapper() { return *m_zlibWrapper; }

  /**
   * @brief Saves the emulator state to a file.
   * @param path File path.
   */
  void SaveState(const std::string &path);

  /**
   * @brief Loads the emulator state from a file.
   * @param path File path.
   */
  void LoadState(const std::string &path);

  /**
   * @brief Emulator statistics with atomic members to prevent race
   * conditions.
   */
  struct Stats {
    std::atomic<uint64_t> instructionsExecuted{
        0};                                  ///< Total instructions executed
    std::atomic<uint64_t> totalCycles{0};    ///< Total CPU cycles
    std::atomic<uint64_t> totalLatencyUs{0}; ///< Total latency (microseconds)
    std::atomic<uint64_t> cacheHits{0};   ///< Cache hits for component access
    std::atomic<uint64_t> cacheMisses{0}; ///< Cache misses for component access
    std::atomic<uint64_t> breakpointHits{0}; ///< Number of breakpoint hits

    // Copy constructor for atomic members
    Stats() = default;
    Stats(const Stats &other)
        : instructionsExecuted(other.instructionsExecuted.load()),
          totalCycles(other.totalCycles.load()),
          totalLatencyUs(other.totalLatencyUs.load()),
          cacheHits(other.cacheHits.load()),
          cacheMisses(other.cacheMisses.load()),
          breakpointHits(other.breakpointHits.load()) {}

    // Assignment operator for atomic members
    Stats &operator=(const Stats &other) {
      if (this != &other) {
        instructionsExecuted.store(other.instructionsExecuted.load());
        totalCycles.store(other.totalCycles.load());
        totalLatencyUs.store(other.totalLatencyUs.load());
        cacheHits.store(other.cacheHits.load());
        cacheMisses.store(other.cacheMisses.load());
        breakpointHits.store(other.breakpointHits.load());
      }
      return *this;
    }
  };

  /**
   * @brief Retrieves emulator statistics.
   * @return Current statistics.
   */
  Stats GetStats() const;

  /**
   * @brief Gets the command processor instance.
   * @return Reference to the command processor.
   */
  CommandProcessor &GetCommandProcessor() { return *m_commandProcessor; }
  /**
   * @brief Gets the tile manager instance.
   * @return Reference to the tile manager.
   */
  TileManager &GetTileManager() { return m_gpu->GetTileManager(); }

  /**
   * @brief Gets the controller manager instance.
   * @return Reference to the controller manager.
   */
  PS4ControllerManager &GetControllerManager() { return *m_controllerManager; }
  /**
   * @brief Gets the audio system instance.
   * @return Reference to the audio system.
   */
  PS4Audio &GetAudio() {
    return *m_audio;
  } /**
     * @brief Gets the PKG installer instance.
     * @return Reference to the PKG installer.
     */
  PKGInstaller &GetPKGInstaller() { return *m_pkgInstaller; }

  /**
   * @brief Reconfigures the emulator with new parameters.
   * @param cpuCount New CPU core count.
   * @param gpuSettings New GPU settings (placeholder).
   * @return True on success, false otherwise.
   */
  bool Reconfigure(uint32_t cpuCount, const std::string &gpuSettings);

  /**
   * @brief Singleton accessor.
   * @return Reference to the emulator instance.
   */
  static PS4Emulator &GetInstance();

  /**
   * @brief Gets the GNM register state.
   * @return Const reference to the GNM register state.
   */
  const GNMRegisterState &GetGNMState() const { return m_gpu->GetGNMState(); }

  /**
   * @brief Gets the mutable GNM register state.
   * @return Reference to the GNM register state.
   */
  GNMRegisterState &GetMutableGNMState() { return m_gpu->GetMutableGNMState(); }

private:
  /**
   * @brief Core execution thread for a CPU.
   * @param coreId CPU core index.
   */
  void CoreThread(uint32_t coreId);
  /**
   * @brief Loads a module.
   * @param path Module path.
   * @param outElf Output LoadedElf structure.
   */
  void LoadModule(const std::string &path, LoadedElf &outElf);

  /**
   * @brief Evaluates a breakpoint condition expression.
   * @param condition The condition string to evaluate.
   * @param cpu The CPU instance for register access.
   * @return True if condition is met, false otherwise.
   */
  bool EvaluateBreakpointCondition(const std::string &condition,
                                   x86_64::X86_64CPU &cpu);

  /**
   * @brief Initializes CPU cores with distributed entry points.
   * @return True on success, false on failure.
   * @details Sets up each CPU with a separate entry point and minimal initial
   * program.
   */
  bool InitializeCPUs();

  /**
   * @brief Handles TSC (Time Stamp Counter) wraparound events.
   * @details Called when the TSC counter wraps around from maximum to minimum
   * value. Adjusts internal timing and synchronization mechanisms to maintain
   * consistency.
   */
  void HandleTSCWrapAround();

private:
  // Core components
  SDL_Window *m_window; ///< SDL window for rendering
  std::vector<std::unique_ptr<x86_64::X86_64CPU>> m_cpus; ///< CPU cores
  std::unique_ptr<PS4MMU> m_mmu;                          ///< MMU instance
  std::unique_ptr<PS4GPU> m_gpu;                          ///< GPU instance
  std::unique_ptr<ps4::TLB> m_tlb;                        ///< TLB instance
  std::unique_ptr<PS4TSC> m_tsc;                          ///< TSC instance
  std::unique_ptr<x86_64::InterruptHandler>
      m_interruptHandler;                         ///< Interrupt handler
  std::unique_ptr<x86_64::IOManager> m_ioManager; ///< I/O manager
  std::unique_ptr<FiberManager> m_fiberManager;   ///< Fiber manager
  std::unique_ptr<PS4ControllerManager>
      m_controllerManager;                          ///< Controller manager
  std::unique_ptr<PS4Audio> m_audio;                ///< Audio system
  std::unique_ptr<OrbisOS> m_os;                    ///< OrbisOS instance
  std::unique_ptr<PS4Filesystem> m_filesystem;      ///< Filesystem instance
  std::unique_ptr<SyscallHandler> m_syscallHandler; ///< Syscall handler

  // Video components
  std::unique_ptr<GNMShaderTranslator>
      m_shaderTranslator;                               ///< Shader translator
  std::unique_ptr<CommandProcessor> m_commandProcessor; ///< Command processor
  std::unique_ptr<TileManager> m_tileManager;           ///< Tile manager

  // Additional components
  std::unique_ptr<TrophyManager> m_trophyManager; ///< Trophy manager
  std::unique_ptr<ZlibWrapper> m_zlibWrapper;     ///< Zlib wrapper
  std::unique_ptr<PKGInstaller> m_pkgInstaller;   ///< PKG installer

  // JIT and debugging
  std::vector<std::unique_ptr<x86_64::X86_64JITCompiler>>
      m_jitCompilers;                                   ///< JIT compilers
  std::vector<Breakpoint> m_breakpoints;                ///< Breakpoints
  std::unordered_map<std::string, int> m_loadedModules; ///< Loaded modules

  // Enhanced components
  std::unique_ptr<EnhancedStats> m_enhancedStats;
  std::unique_ptr<DeadlockDetector> m_deadlockDetector;
  std::thread m_resourceMonitorThread;
  std::vector<std::thread> m_coreThreads; ///< Core execution threads

  // State tracking
  bool m_running = false;
  bool m_paused = false;
  mutable std::shared_mutex m_emulatorMutex;
  mutable Stats m_stats;

  /**
   * @brief Helper method to validate system requirements.
   * @return True if requirements are met, false otherwise.
   */
  bool ValidateSystemRequirements();

  /**
   * @brief Initializes a component with rollback capability.
   * @param initFunc Function to initialize the component.
   * @param cleanupFunc Function to cleanup the component.
   * @param cleanupFunctions Vector to store cleanup functions.
   * @param componentName Name of the component to initialize.
   * @return True on success, false on failure.
   */
  bool InitializeComponentWithRollback(
      std::function<bool()> initFunc, std::function<void()> cleanupFunc,
      std::vector<std::function<void()>> &cleanupFunctions,
      const std::string &componentName);

  /**
   * @brief Initializes a component with timeout protection.
   * @param initFunc Function to initialize the component.
   * @param cleanupFunc Function to cleanup the component.
   * @param cleanupFunctions Vector to store cleanup functions.
   * @param componentName Name of the component to initialize.
   * @param timeoutSeconds Timeout in seconds for initialization.
   * @return True on success, false on failure or timeout.
   */
  bool InitializeComponentWithTimeout(
      std::function<bool()> initFunc, std::function<void()> cleanupFunc,
      std::vector<std::function<void()>> &cleanupFunctions,
      const std::string &componentName, int timeoutSeconds = 30);

  /**
   * @brief Monitors resources and detects deadlocks.
   */
  void ResourceMonitorThread();

  /**
   * @brief Executes a single cycle of the emulator.
   */
  void ExecuteCycle();

  static PS4Emulator *s_instance; ///< Singleton instance
};

/**
 * @brief Structure for enhanced statistics monitoring.
 */
struct EnhancedStats : PS4Emulator::Stats {
  std::atomic<uint64_t> slowCycles{0};
  std::atomic<uint64_t> cycleErrors{0};
  std::atomic<uint64_t> memoryPressureEvents{0};
  std::atomic<uint64_t> jitCompilationTime{0};
  std::atomic<uint64_t> contextSwitches{0};
  std::atomic<double> averageFPS{0.0};
  std::atomic<double> minFPS{999.0};
  std::atomic<double> maxFPS{0.0};

  // Performance histograms
  std::array<std::atomic<uint64_t>, 10>
      frameTimeHistogram{}; // 0-10ms, 10-20ms, etc.
  std::array<std::atomic<uint64_t>, 8>
      cpuLoadHistogram{}; // Per-core load distribution
};

/**
 * @brief Deadlock detector class.
 */
class DeadlockDetector {
public:
  DeadlockDetector() = default;

  /**
   * @brief Checks for deadlocks in the emulator.
   * @return True if deadlocks are detected, false otherwise.
   */
  bool CheckForDeadlocks();

  /**
   * @brief Updates the state of a thread in the deadlock detector.
   * @param threadId ID of the thread.
   * @param waitingOn Resource the thread is waiting on.
   */
  void UpdateThreadState(uint64_t threadId, uint64_t waitingOn);

  /**
   * @brief Clears the state of a thread in the deadlock detector.
   * @param threadId ID of the thread.
   */
  void ClearThreadState(uint64_t threadId);

private:
  std::unordered_map<uint64_t, uint64_t>
      waitGraph;            ///< Wait-for graph for deadlock detection
  std::mutex detectorMutex; ///< Mutex for thread-safe access
};

} // namespace ps4
