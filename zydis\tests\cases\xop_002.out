== [    BASIC ] ============================================================================================
   MNEMONIC: vpshaw [ENC: XOP, MAP: XOP9, OPC: 0x99]
     LENGTH:  5
        SSZ: 64
       EOSZ: 64
       EASZ: 64
   CATEGORY: XOP
    ISA-SET: XOP
    ISA-EXT: XOP
 EXCEPTIONS: NONE
 ATTRIBUTES: HAS_MODRM HAS_XOP ACCEPTS_SEGMENT 
  OPTIMIZED: 8F 69 D8 99 19 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    128      8      16       INT                        xmm11
 1   REGISTER    EXPLICIT       R        NDSNDD    128      8      16       INT                         xmm4
 2     MEMORY    EXPLICIT       R      MODRM_RM    128      8      16       INT  TYPE  =                 MEM
                                                                                 SEG   =                  ds
                                                                                 BASE  =                 rcx
                                                                                 INDEX =                none
                                                                                 SCALE =                   0
                                                                                 DISP  =  0x0000000000000000
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 128
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vpshaw (%rcx), %xmm4, %xmm11
   RELATIVE: vpshaw (%rcx), %xmm4, %xmm11

== [    INTEL ] ============================================================================================
   ABSOLUTE: vpshaw xmm11, xmm4, xmmword ptr ds:[rcx]
   RELATIVE: vpshaw xmm11, xmm4, xmmword ptr ds:[rcx]

== [ SEGMENTS ] ============================================================================================
8F 69 D8 99 19 
:        :  :..MODRM
:        :..OPCODE
:..XOP
