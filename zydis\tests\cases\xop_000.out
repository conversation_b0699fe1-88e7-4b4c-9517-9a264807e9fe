== [    BASIC ] ============================================================================================
   MNEMONIC: vpcomuq [ENC: XOP, MAP: XOP8, OPC: 0xEF]
     LENGTH:  7
        SSZ: 64
       EOSZ: 32
       EASZ: 64
   CATEGORY: XOP
    ISA-SET: XOP
    ISA-EXT: XOP
 EXCEPTIONS: NONE
 ATTRIBUTES: HAS_MODRM HAS_XOP ACCEPTS_SEGMENT 
  OPTIMIZED: 8F E8 60 EF 4E 3D C0 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    128      2      64      UINT                         xmm1
 1   REGISTER    EXPLICIT       R        NDSNDD    128      2      64      UINT                         xmm3
 2     MEMORY    EXPLICIT       R      MODRM_RM    128      2      64      UINT  TYPE  =                 MEM
                                                                                 SEG   =                  ds
                                                                                 BASE  =                 rsi
                                                                                 INDEX =                none
                                                                                 SCALE =                   0
                                                                                 DISP  =  0x000000000000003D
 3  IMMEDIATE    EXPLICIT       R         UIMM8      8      1       8      UINT  [U A  8] 0x00000000000000C0
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 128
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vpcomuq $0xC0, 0x3D(%rsi), %xmm3, %xmm1
   RELATIVE: vpcomuq $0xC0, 0x3D(%rsi), %xmm3, %xmm1

== [    INTEL ] ============================================================================================
   ABSOLUTE: vpcomuq xmm1, xmm3, xmmword ptr ds:[rsi+0x3D], 0xC0
   RELATIVE: vpcomuq xmm1, xmm3, xmmword ptr ds:[rsi+0x3D], 0xC0

== [ SEGMENTS ] ============================================================================================
8F E8 60 EF 4E 3D C0 
:        :  :  :  :..IMM
:        :  :  :..DISP
:        :  :..MODRM
:        :..OPCODE
:..XOP
