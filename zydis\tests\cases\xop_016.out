== [    BASIC ] ============================================================================================
   MNEMONIC: vprotw [ENC: XOP, MAP: XOP9, OPC: 0x91]
     LENGTH:  5
        SSZ: 64
       EOSZ: 64
       EASZ: 64
   CATEGORY: XOP
    ISA-SET: XOP
    ISA-EXT: XOP
 EXCEPTIONS: NONE
 ATTRIBUTES: HAS_MODRM HAS_XOP ACCEPTS_SEGMENT 
  OPTIMIZED: 8F E9 F8 91 26 

== [ OPERANDS ] ============================================================================================
##       TYPE  VISIBILITY  ACTION      ENCODING   SIZE  NELEM  ELEMSZ  ELEMTYPE                        VALUE
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------
 0   <USER>    <GROUP>       W     MODRM_REG    128      8      16      UINT                         xmm4
 1   REGISTER    EXPLICIT       R        NDSNDD    128      8      16      UINT                         xmm0
 2     MEMORY    EXPLICIT       R      MODRM_RM    128      8      16      UINT  TYPE  =                 MEM
                                                                                 SEG   =                  ds
                                                                                 BASE  =                 rsi
                                                                                 INDEX =                none
                                                                                 SCALE =                   0
                                                                                 DISP  =  0x0000000000000000
--  ---------  ----------  ------  ------------   ----  -----  ------  --------  ---------------------------

== [      AVX ] ============================================================================================
  VECTORLEN: 128
  BROADCAST: NONE

== [      ATT ] ============================================================================================
   ABSOLUTE: vprotw (%rsi), %xmm0, %xmm4
   RELATIVE: vprotw (%rsi), %xmm0, %xmm4

== [    INTEL ] ============================================================================================
   ABSOLUTE: vprotw xmm4, xmm0, xmmword ptr ds:[rsi]
   RELATIVE: vprotw xmm4, xmm0, xmmword ptr ds:[rsi]

== [ SEGMENTS ] ============================================================================================
8F A9 F8 91 26 
:        :  :..MODRM
:        :..OPCODE
:..XOP
